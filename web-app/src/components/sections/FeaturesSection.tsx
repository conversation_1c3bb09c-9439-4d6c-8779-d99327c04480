"use client";

interface FeatureCardProps {
    title: string;
    description: string;
    icon?: React.ReactNode; // Optional: for future icon integration
}

const FeatureCard: React.FC<FeatureCardProps> = ({ title, description, icon }) => (
    <div className="bg-card border border-border rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
        {icon && <div className="mb-4 text-primary text-3xl">{icon}</div>}
        <h3 className="text-xl font-semibold mb-2 text-primary">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
    </div>
);

export const FeaturesSection = () => {
    const features: FeatureCardProps[] = [
        {
            title: "AI 图像生成",
            description: "通过简单的自然语言描述，轻松创造出全新的、独一无二的 AI 图像。",
        },
        {
            title: "AI 图像编辑",
            description: "上传您现有的图片，使用自然语言指令进行智能编辑和风格转换。",
        },
        {
            title: "多样化风格选择",
            description: "支持写实照片、油画、3D卡通、像素艺术等多种艺术风格，满足您的创意需求。",
        },
        {
            title: "统一创作体验",
            description: "将图像生成与编辑功能无缝融合在同一个简洁直观的界面中。",
        },
        {
            title: "创作历史追溯",
            description: "自动保存您的创作历史和提示词，方便回顾、重用和迭代您的想法。",
        },
        {
            title: "自然语言驱动",
            description: "无需复杂的参数调整，只需用日常语言描述您的想法，AI 助您实现。",
        },
    ];

    return (
        <section className="w-full max-w-5xl mt-20 py-12">
            <div className="text-center mb-12">
                <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                    LuckyX AI 的核心特点
                </h2>
                <p className="mt-4 text-lg text-muted-foreground">
                    探索我们平台强大的功能，释放您的无限创意潜能。
                </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {features.map((feature) => (
                    <FeatureCard key={feature.title} title={feature.title} description={feature.description} />
                ))}
            </div>
        </section>
    );
};
