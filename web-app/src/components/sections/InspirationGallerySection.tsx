"use client";

import Image from "next/image";

interface GalleryImage {
    src: string;
    alt: string;
    prompt: string; // A short example prompt that could have generated this
}

export const InspirationGallerySection = () => {
    const images: GalleryImage[] = [
        {
            src: "https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YWJzdHJhY3R8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60",
            alt: "Abstract swirling colors",
            prompt: "Vibrant abstract swirls, cosmic energy, detailed",
        },
        {
            src: "https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YmVhY2h8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60",
            alt: "Tropical beach at sunset",
            prompt: "Serene tropical beach, golden sunset, palm trees silhouette, photorealistic",
        },
        {
            src: "https://images.unsplash.com/photo-1517423568366-8b83523034fd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y3V0ZSUyMGRvZ3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60",
            alt: "Cute dog wearing a hat",
            prompt: "Fluffy Corgi wearing a tiny birthday hat, studio lighting, adorable",
        },
        {
            src: "https://images.unsplash.com/photo-1550745165-9bc0b252726c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8cmV0cm98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60",
            alt: "Retro gaming console",
            prompt: "Vintage arcade game console, neon lights, 80s retro wave, pixel art style",
        },
        {
            src: "https://images.unsplash.com/photo-1604014237800-1c9102c219da?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8Zm9vZHxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60",
            alt: "Delicious looking pizza",
            prompt: "Gourmet pizza with fresh basil and mozzarella, close-up, food photography",
        },
        {
            src: "https://images.unsplash.com/photo-1542044896530-05d85be9b11a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8Y2F0fGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60",
            alt: "Cat in a spacesuit",
            prompt: "A curious cat wearing a detailed astronaut helmet, looking at Earth from space, digital art",
        },
    ];

    return (
        <section className="w-full max-w-6xl mt-20 py-12">
            <div className="text-center mb-12">
                <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                    灵感画廊
                </h2>
                <p className="mt-4 text-lg text-muted-foreground">
                    探索由 AI 生成的各种图像，激发您的创作灵感。
                </p>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {images.map((image, index) => (
                    <div key={index} className="group relative rounded-lg overflow-hidden border border-border shadow-md hover:shadow-xl transition-all duration-300">
                        <Image
                            src={image.src}
                            alt={image.alt}
                            width={500}
                            height={500}
                            className="object-cover w-full h-64 sm:h-72 transition-transform duration-300 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                            <p className="text-white text-sm font-medium line-clamp-2">
                                {image.prompt}
                            </p>
                        </div>
                    </div>
                ))}
            </div>
        </section>
    );
};
