"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Textarea } from "@/components/ui/textarea";
import { useRef, useState } from "react";


// 高质量开关组件
const HighQualityToggle = ({ isHighQuality, setIsHighQuality }: { isHighQuality: boolean, setIsHighQuality: (value: boolean) => void }) => {
    // 使用一个div包装Switch，处理点击事件
    const handleClick = () => {
        setIsHighQuality(!isHighQuality);
    };

    return (
        <div
            className="group flex items-center space-x-1.5 px-2.5 py-1 rounded-full bg-primary/10 hover:bg-primary/20 border border-primary/30 hover:border-primary/50 shadow-sm hover:shadow-md transition-all duration-150 cursor-pointer"
            onClick={handleClick}
        >
            <CrownIcon />
            <span className="text-xs sm:text-sm font-medium text-primary group-hover:text-primary/90">高质量</span>
            <div className="ml-auto flex items-center">
                {/* 使用一个静态的div来模拟Switch的外观，但不使用实际的Switch组件 */}
                <div className={`inline-flex h-[1.15rem] w-8 items-center rounded-full border border-transparent shadow-xs transition-all scale-75 sm:scale-90 ${isHighQuality ? 'bg-primary' : 'bg-input dark:bg-input/80'}`}>
                    <span className={`block h-4 w-4 rounded-full bg-white transition-transform ${isHighQuality ? 'translate-x-[calc(100%-2px)]' : 'translate-x-0'}`} />
                </div>
            </div>
        </div>
    );
};

// Placeholder SVGs
const EraserIcon = () => (
    <svg fill="currentColor" width="14px" height="14px" viewBox="0 0 299.289 299.289" xmlSpace="preserve" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
        <g>
            <g>
                <path d="M290.422,79.244L220.034,8.857c-11.794-11.795-30.986-11.795-42.78,0C175.866,10.245,12.971,173.14,8.867,177.244 c-11.822,11.821-11.824,30.957,0,42.78l70.388,70.388c11.821,11.822,30.957,11.824,42.78,0 c1.046-1.046,165.357-165.357,168.388-168.388C302.244,110.203,302.246,91.066,290.422,79.244z M110.367,278.744 c-5.374,5.373-14.071,5.373-19.446,0l-70.388-70.388c-5.373-5.374-5.375-14.071,0-19.446l34.61-34.61l89.834,89.834 L110.367,278.744z M278.755,110.357l-122.111,122.11l-89.833-89.833l122.11-122.111c5.374-5.374,14.071-5.374,19.446,0 l70.388,70.388C284.129,96.285,284.129,104.983,278.755,110.357z" />
            </g>
        </g>
    </svg>
);
const AspectRatioIcon = () => <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14 2H2C1.44772 2 1 2.44772 1 3V13C1 13.5523 1.44772 14 2 14H14C14.5523 14 15 13.5523 15 13V3C15 2.44772 14.5523 2 14 2ZM13 4V12H3V4H13Z" fill="currentColor" /></svg>;
const StyleIcon = () => <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0ZM1.5 8C1.5 4.41015 4.41015 1.5 8 1.5V14.5C4.41015 14.5 1.5 11.5899 1.5 8Z" fill="currentColor" /></svg>;
const ImageIcon = () => <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>;
const SquareIcon = () => <svg width="16" height="16" viewBox="0 0 16 16"><rect x="3" y="3" width="10" height="10" fill="currentColor" /></svg>;
const WideIcon = () => <svg width="16" height="16" viewBox="0 0 16 16"><rect x="1" y="5" width="14" height="6" fill="currentColor" /></svg>;
const TallIcon = () => <svg width="16" height="16" viewBox="0 0 16 16"><rect x="5" y="1" width="6" height="14" fill="currentColor" /></svg>;
const NoStyleIcon = () => <svg width="16" height="16" viewBox="0 0 16 16"><path d="M8 1.5a6.5 6.5 0 100 13 6.5 6.5 0 000-13zM1 8a7 7 0 1114 0A7 7 0 011 8z" fillRule="evenodd" clipRule="evenodd" fill="currentColor" /><path d="M4.5 7.5l7 1-1 1-7-1 1-1z" fill="currentColor" /></svg>;
const CloseIcon = () => <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>;
const CrownIcon = () => <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" className="text-primary opacity-80 group-hover:opacity-100"><path d="M2 4l3 12h14l3-12-6 7-4-7-4 7-6-7zm0 0L12 18 22 4"></path></svg>;


const dimensionOptions = [
    { value: "square", label: "Square", icon: <SquareIcon /> },
    { value: "square-hd", label: "Square HD", icon: <SquareIcon /> },
    { value: "portrait-3-4", label: "Portrait 3:4", icon: <TallIcon /> },
    { value: "portrait-9-16", label: "Portrait 9:16", icon: <TallIcon /> },
    { value: "landscape-4-3", label: "Landscape 4:3", icon: <WideIcon /> },
    { value: "landscape-16-9", label: "Landscape 16:9", icon: <WideIcon /> },
];

const styleOptions = [
    { value: "none", label: "No Style", icon: <NoStyleIcon /> },
    { value: "photorealistic", label: "Photorealistic", icon: <StyleIcon /> },
    { value: "digital-art", label: "Digital Art", icon: <StyleIcon /> },
    { value: "anime", label: "Anime", icon: <StyleIcon /> },
];

interface FormErrors {
    prompt?: string;
}

const PROMPT_MIN_LENGTH = 5;
const PROMPT_MAX_LENGTH = 1000;

export const GenerationForm = () => {
    const [prompt, setPrompt] = useState("");
    const [aspectRatio, setAspectRatio] = useState(dimensionOptions[0].value);
    const [style, setStyle] = useState(styleOptions[0].value);
    const [isHighQuality, setIsHighQuality] = useState(false);
    const [isDimensionPopoverOpen, setIsDimensionPopoverOpen] = useState(false);
    const [isStylePopoverOpen, setIsStylePopoverOpen] = useState(false);
    const [formErrors, setFormErrors] = useState<FormErrors>({});
    const [uploadedImagePreviewUrl, setUploadedImagePreviewUrl] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);


    const validateForm = (): boolean => {
        const errors: FormErrors = {};
        const trimmedPrompt = prompt.trim();
        if (!trimmedPrompt && !uploadedImagePreviewUrl) {
            // errors.prompt = "请输入提示词或上传图片。";
        } else if (trimmedPrompt && trimmedPrompt.length < PROMPT_MIN_LENGTH) {
            errors.prompt = `提示词至少需要 ${PROMPT_MIN_LENGTH} 个字符。`;
        } else if (trimmedPrompt.length > PROMPT_MAX_LENGTH) {
            errors.prompt = `提示词不能超过 ${PROMPT_MAX_LENGTH} 个字符。`;
        }
        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (!validateForm()) {
            return;
        }
        console.log({ prompt, aspectRatio, style, isHighQuality, uploadedImagePreviewUrl });
    };

    const handleClear = () => {
        setPrompt("");
        setUploadedImagePreviewUrl(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
        setFormErrors({});
    };

    const handlePromptChange = (newPrompt: string) => {
        setPrompt(newPrompt);
        if (formErrors.prompt) {
            const currentErrors = { ...formErrors };
            delete currentErrors.prompt;
            setFormErrors(currentErrors);
        }
    };

    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setUploadedImagePreviewUrl(reader.result as string);
            };
            reader.readAsDataURL(file);
            if (formErrors.prompt && !prompt.trim()) {
                const currentErrors = { ...formErrors };
                delete currentErrors.prompt;
                setFormErrors(currentErrors);
            }
        } else {
            setUploadedImagePreviewUrl(null);
        }
    };

    const removeUploadedImage = () => {
        setUploadedImagePreviewUrl(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };

    const selectedDimension = dimensionOptions.find(opt => opt.value === aspectRatio) || dimensionOptions[0];
    const selectedStyle = styleOptions.find(opt => opt.value === style) || styleOptions[0];
    const isSubmitDisabled = (!prompt.trim() && !uploadedImagePreviewUrl) || Object.keys(formErrors).length > 0;


    return (
        <div className="w-full rounded-lg border border-border bg-card p-4 sm:p-6 md:p-8 shadow-xl">
            <h2 className="mb-6 text-2xl sm:text-3xl font-bold text-center text-primary tracking-tight">
                开始您的 AI 创作之旅
            </h2>
            <form onSubmit={handleSubmit} className="space-y-4 md:space-y-5">
                {uploadedImagePreviewUrl && (
                    <div className="relative group w-full max-w-[200px] sm:max-w-xs mx-auto border-2 border-dashed border-primary/40 rounded-lg p-1.5 bg-background/20">
                        <img
                            src={uploadedImagePreviewUrl}
                            alt="Uploaded preview"
                            className="rounded object-contain max-h-40 sm:max-h-48 w-full"
                        />
                        <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute top-1.5 right-1.5 h-6 w-6 sm:h-7 sm:w-7 bg-background/60 hover:bg-destructive/70 text-destructive-foreground hover:text-white rounded-full opacity-60 group-hover:opacity-100 transition-opacity"
                            onClick={removeUploadedImage}
                            aria-label="Remove uploaded image"
                        >
                            <CloseIcon />
                        </Button>
                    </div>
                )}

                <div className="space-y-1.5">
                    <Label htmlFor="prompt" className="text-base font-medium sr-only">
                        魔法提示词
                    </Label>
                    <div className="relative rounded-md border-2 border-input hover:border-primary/50 focus-within:border-primary focus-within:ring-2 focus-within:ring-primary/20 transition-all">
                        <Textarea
                            id="prompt"
                            name="prompt"
                            autoFocus={!uploadedImagePreviewUrl}
                            placeholder="输入您的魔法提示词，或上传图片进行编辑..."
                            value={prompt}
                            onChange={(e) => handlePromptChange(e.target.value)}
                            className="text-lg md:text-xl font-medium w-full bg-transparent border-0 focus:ring-0 resize-none p-4 sm:p-5 leading-relaxed min-h-[20vh] sm:min-h-[22vh]"
                        />
                        {formErrors.prompt && <p className="px-4 sm:px-5 pb-1.5 text-sm text-destructive">{formErrors.prompt}</p>}
                    </div>
                </div>

                {/* Single Control Row with Border */}
                <div className="border border-border rounded-lg p-2.5 sm:p-3 space-y-2 md:space-y-0 md:flex md:flex-wrap md:items-center md:justify-between md:gap-2">
                    {/* Left Group: Settings */}
                    <div className="flex flex-wrap items-center gap-2 sm:gap-3">
                        <HighQualityToggle
                            isHighQuality={isHighQuality}
                            setIsHighQuality={setIsHighQuality}
                        />
                        <Button
                            type="button"
                            size="sm" // Keep size consistent
                            className="group flex items-center space-x-1.5 px-2.5 py-1 rounded-full bg-primary/10 hover:bg-primary/20 border border-primary/30 hover:border-primary/50 text-primary shadow-sm hover:shadow-md transition-all duration-150 cursor-pointer text-xs sm:text-sm font-medium"
                            onClick={() => fileInputRef.current?.click()}
                        >
                            <ImageIcon />
                            <span className="text-primary group-hover:text-primary/90">上传</span>
                        </Button>
                        <input
                            type="file"
                            id="image-upload-input"
                            ref={fileInputRef}
                            hidden
                            accept="image/*"
                            onChange={handleImageUpload}
                        />
                        <Popover open={isDimensionPopoverOpen} onOpenChange={setIsDimensionPopoverOpen}>
                            <PopoverTrigger asChild>
                                <Button size="sm" className="group flex items-center space-x-1.5 px-2.5 py-1 rounded-full bg-primary/10 hover:bg-primary/20 border border-primary/30 hover:border-primary/50 text-white shadow-sm hover:shadow-md transition-all duration-150 cursor-pointer text-xs sm:text-sm font-medium">
                                    {/* Ensure icon inside PopoverTrigger also uses white if needed, or adjust SVG fill="currentColor" and let text-white apply */}
                                    <div className="text-white group-hover:text-white/90">{selectedDimension.icon}</div>
                                    <span className="text-white group-hover:text-white/90 hidden sm:inline">{selectedDimension.label}</span>
                                    <span className="text-white group-hover:text-white/90 sm:hidden">尺寸</span>
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <div className="flex flex-col">
                                    {dimensionOptions.map((option) => (
                                        <Button
                                            key={option.value}
                                            variant="ghost"
                                            className="justify-start px-3 py-1.5 sm:py-2 text-xs sm:text-sm"
                                            onClick={() => {
                                                setAspectRatio(option.value);
                                                setIsDimensionPopoverOpen(false);
                                            }}
                                        >
                                            {option.icon}
                                            <span className="ml-2">{option.label}</span>
                                        </Button>
                                    ))}
                                </div>
                            </PopoverContent>
                        </Popover>

                        <Popover open={isStylePopoverOpen} onOpenChange={setIsStylePopoverOpen}>
                            <PopoverTrigger asChild>
                                <Button size="sm" className="group flex items-center space-x-1.5 px-2.5 py-1 rounded-full bg-primary/10 hover:bg-primary/20 border border-primary/30 hover:border-primary/50 text-white shadow-sm hover:shadow-md transition-all duration-150 cursor-pointer text-xs sm:text-sm font-medium">
                                    {/* Ensure icon inside PopoverTrigger also uses white if needed, or adjust SVG fill="currentColor" and let text-white apply */}
                                    <div className="text-white group-hover:text-white/90">{selectedStyle.icon}</div>
                                    <span className="text-white group-hover:text-white/90 hidden sm:inline">{selectedStyle.label}</span>
                                    <span className="text-white group-hover:text-white/90 sm:hidden">风格</span>
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <div className="flex flex-col">
                                    {styleOptions.map((option) => (
                                        <Button
                                            key={option.value}
                                            variant="ghost"
                                            className="justify-start px-3 py-1.5 sm:py-2 text-xs sm:text-sm"
                                            onClick={() => {
                                                setStyle(option.value);
                                                setIsStylePopoverOpen(false);
                                            }}
                                        >
                                            {option.icon}
                                            <span className="ml-2">{option.label}</span>
                                        </Button>
                                    ))}
                                </div>
                            </PopoverContent>
                        </Popover>
                    </div>

                    {/* Right Group: Actions - For larger screens, this group will be pushed to the right by justify-between on parent */}
                    {/* On smaller screens (handled by flex-wrap on parent), this will naturally flow below or wrap */}
                    <div className="flex items-center justify-end gap-2 sm:gap-3 pt-2 md:pt-0 md:ml-auto">
                        <Button
                            type="button"
                            variant="ghost" // Ghost variant for transparent background
                            onClick={handleClear}
                            className="group flex items-center space-x-1.5 rounded-full border border-muted-foreground/20 text-muted-foreground/70 hover:border-destructive hover:text-destructive shadow-sm transform transition-all duration-150 px-4 py-2 text-sm cursor-pointer !bg-transparent hover:!bg-transparent"
                        >
                            <EraserIcon />
                            <span className="group-hover:text-destructive">清除</span>
                        </Button>
                        <Button
                            type="submit"
                            disabled={isSubmitDisabled}
                            className={`rounded-full shadow-lg hover:shadow-xl transform transition-all duration-150 px-4 py-2 text-sm font-semibold flex items-center justify-center
                                        disabled:opacity-60 disabled:bg-stone-700 disabled:text-stone-300 disabled:border-stone-600 disabled:cursor-not-allowed disabled:!pointer-events-auto
                                        bg-accent text-accent-foreground hover:bg-accent/85 focus-visible:ring-2 focus-visible:ring-accent focus-visible:ring-offset-2 focus-visible:ring-offset-background
                                        ${isSubmitDisabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                        >
                            ✨ 生成 ✨
                        </Button>
                    </div>
                </div>
            </form>
        </div>
    );
};
