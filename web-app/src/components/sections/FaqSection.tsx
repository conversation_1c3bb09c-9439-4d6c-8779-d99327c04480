"use client";

import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion"; // Assuming Shadcn UI Accordion

interface FaqItem {
    question: string;
    answer: string;
    value: string;
}

export const FaqSection = () => {
    const faqs: FaqItem[] = [
        {
            value: "item-1",
            question: "LuckyX AI 是如何工作的？",
            answer:
                "LuckyX AI 利用先进的人工智能模型，将您的文本描述转换为图像。您只需输入想要看到的场景或概念，AI 就会为您生成视觉作品。对于图像编辑，AI 会理解您的修改指令并智能地应用到您上传的图片上。",
        },
        {
            value: "item-2",
            question: "我需要有设计经验才能使用吗？",
            answer:
                "完全不需要！LuckyX AI 的设计初衷就是让每个人都能轻松创作。无论您是专业设计师还是完全没有经验的初学者，都可以通过简单的自然语言描述来生成和编辑图像。",
        },
        {
            value: "item-3",
            question: "生成的图像可以用于商业用途吗？",
            answer:
                "这取决于您选择的服务套餐。我们的免费套餐生成的图像可能带有水印且限制商业用途，而付费套餐通常会提供无水印的高清图像以及商业使用许可。具体请参考我们的服务条款。",
        },
        {
            value: "item-4",
            question: "支持哪些图像风格和编辑类型？",
            answer:
                "我们支持多种图像风格，如写实照片、油画、3D卡通、像素艺术等。编辑功能包括但不限于风格转换、内容添加/移除、图像扩展、细节增强等。我们会持续更新和扩展支持的风格与功能。",
        },
        {
            value: "item-5",
            question: "我的创作历史会保存在哪里？安全吗？",
            answer:
                "您的创作历史（包括提示词和设置，但不包括图像文件本身）默认保存在您的浏览器本地存储中，这意味着只有您自己可以访问。我们不会在服务器上存储您的图像文件，以最大程度保护您的隐私。",
        },
    ];

    return (
        <section className="w-full max-w-3xl mt-20 py-12">
            <div className="text-center mb-12">
                <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                    常见问题 (FAQ)
                </h2>
                <p className="mt-4 text-lg text-muted-foreground">
                    在这里找到您关于 LuckyX AI 的常见疑问解答。
                </p>
            </div>
            <Accordion type="single" collapsible className="w-full">
                {faqs.map((faq) => (
                    <AccordionItem value={faq.value} key={faq.value}>
                        <AccordionTrigger className="text-left hover:no-underline">
                            {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-muted-foreground">
                            {faq.answer}
                        </AccordionContent>
                    </AccordionItem>
                ))}
            </Accordion>
        </section>
    );
};
