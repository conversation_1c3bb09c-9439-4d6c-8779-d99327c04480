"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    Di<PERSON>Footer,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from "@/components/ui/dialog";
import Image from "next/image";
import { useState } from "react";

interface LoginModalProps {
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
    trigger?: React.ReactNode; // Optional trigger if not controlled externally
}

export function LoginModal({ open, onOpenChange, trigger }: LoginModalProps) {
    const [isLoading, setIsLoading] = useState(false);

    const handleGoogleSignIn = async () => {
        try {
            setIsLoading(true);

            // 延迟重定向，以便用户能看到加载动画
            // 不关闭模态框，直接重定向，保持视觉连贯性
            setTimeout(() => {
                // 重定向到自定义加载页面
                window.location.href = "/auth/loading?provider=google&callbackUrl=/";
                // 不再调用 onOpenChange(false)，保持模态框显示直到页面跳转
            }, 800); // 800毫秒的延迟，足够看到加载动画但不会太长

        } catch (error) {
            console.error("登录失败:", error);
            setIsLoading(false);
        }
    };

    // Note: Shadcn DialogOverlay is part of the Dialog component and gets styled by default.
    // We will focus on DialogContent styling.
    // The overlay effect (dimming/blurring background) is typically handled by DialogOverlay.
    // If more specific control over the overlay is needed, one might need to customize the Dialog component itself or use global CSS.
    // For now, we assume the default overlay + DialogContent styling will achieve the desired effect.

    const dialogContent = (
        // Attempting style similar to Raphael AI: Lighter modal on dark blurred background
        <DialogContent className="sm:max-w-xs p-0 overflow-hidden rounded-lg shadow-xl
                                bg-[#2A2A2E]  /* Dark gray, similar to Raphael AI modal */
                                border-neutral-700 /* Subtle border */
                                text-neutral-100"> {/* Light text for dark modal */}
            <DialogHeader className="px-6 pt-6 pb-4">
                <DialogTitle className="text-center text-lg font-medium text-neutral-100"> {/* Adjusted title size */}
                    登录 LuckyX AI
                </DialogTitle>
            </DialogHeader>
            <div className="px-6 pb-6">
                <Button
                    variant="outline"
                    className="w-full h-11 text-sm font-medium group flex items-center justify-center
                               bg-neutral-700/50 /* Semi-transparent darker gray button */
                               border-neutral-600 /* Button border */
                               text-neutral-100 /* Light text on button */
                               hover:bg-neutral-600/70 /* Hover: slightly more opaque */
                               hover:border-neutral-500
                               focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 focus:ring-offset-[#2A2A2E]
                               transition-colors duration-150 rounded-md cursor-pointer"
                    onClick={handleGoogleSignIn}
                    disabled={isLoading}
                >
                    {isLoading ? (
                        <>
                            <div className="w-5 h-5 mr-2 border-2 border-t-transparent border-white rounded-full animate-spin"></div>
                            正在跳转...
                        </>
                    ) : (
                        <>
                            <Image
                                src="/google-logo.svg"
                                alt="Google"
                                width={18}
                                height={18}
                                className="mr-2"
                            />
                            使用 Google 登录
                        </>
                    )}
                </Button>
            </div>
            <DialogFooter className="text-xs text-neutral-400 text-center px-6 py-4
                                 bg-[#252528] /* Slightly different dark for footer */
                                 border-t border-neutral-700">
                <p>
                    点击“使用 Google 登录”，即表示您同意我们的
                    <a href="/terms" className="underline hover:text-[oklch(var(--primary))] transition-colors">服务条款</a> 和
                    <a href="/privacy" className="underline hover:text-[oklch(var(--primary))] transition-colors">隐私政策</a>。
                </p>
            </DialogFooter>
        </DialogContent>
    );

    if (trigger) {
        return (
            <Dialog open={open} onOpenChange={onOpenChange}>
                <DialogTrigger asChild>{trigger}</DialogTrigger>
                {/* Shadcn Dialog includes an overlay by default. We can style DialogContent for prominence. */}
                {dialogContent}
            </Dialog>
        );
    }

    // For externally controlled dialog (no trigger prop)
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            {/* Shadcn Dialog includes an overlay by default. */}
            {dialogContent}
        </Dialog>
    );
}
