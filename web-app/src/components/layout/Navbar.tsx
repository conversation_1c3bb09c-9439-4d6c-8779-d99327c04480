"use client";

import { LoginModal } from '@/components/auth/LoginModal';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"; // Assuming avatar is also a shadcn component
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useGoogleOneTap } from '@/hooks/useGoogleOneTap'; // Import the hook
import { signOut, useSession } from "next-auth/react";
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

// Placeholder for a Logo component or an SVG
const Logo = () => (
    <Image src="/favicon.svg" alt="LuckyX AI Logo" width={32} height={32} className="h-8 w-8" />
);

export const Navbar = () => {
    const { data: session, status } = useSession();
    const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

    useGoogleOneTap(); // Call the hook to initialize One Tap

    const getInitials = (name?: string | null) => {
        if (!name) return "LX";
        const names = name.split(' ');
        if (names.length === 1) return name.substring(0, 2).toUpperCase();
        return names[0][0].toUpperCase() + names[names.length - 1][0].toUpperCase();
    };

    return (
        <>
            <header className="sticky top-0 z-50 w-full border-b border-primary/30 bg-background/85 backdrop-blur-lg supports-[backdrop-filter]:bg-background/60 shadow-lg shadow-primary/10 px-6">
                <div className="flex h-20 items-center justify-between"> {/* This div spans the width inside the header's padding */}
                    {/* Left Section: Logo */}
                    <Link href="/" className="flex items-center space-x-3">
                        <Logo />
                        <span className="text-xl font-bold sm:inline-block text-foreground">
                            LuckyX AI
                        </span>
                    </Link>

                    {/* Middle Section: Navigation Links */}
                    <nav className="hidden md:flex items-center justify-center gap-2 lg:gap-3 p-1 bg-background/70 border border-primary/50 rounded-lg shadow-lg shadow-primary/30 ring-1 ring-primary/50 ring-inset">
                        <Link href="#features" className="flex items-center gap-2 transition-all duration-200 ease-in-out text-foreground font-medium hover:text-primary hover:bg-primary/30 px-4 py-2 rounded-md hover:scale-105 transform group">
                            <Image src="/feature.svg" alt="Features Icon" width={18} height={18} className="opacity-80 group-hover:opacity-100 transition-opacity" />
                            特点
                        </Link>
                        <Link href="#faq" className="flex items-center gap-2 transition-all duration-200 ease-in-out text-foreground font-medium hover:text-primary hover:bg-primary/30 px-4 py-2 rounded-md hover:scale-105 transform group">
                            <Image src="/faq.svg" alt="FAQ Icon" width={18} height={18} className="opacity-80 group-hover:opacity-100 transition-opacity" />
                            常见问题
                        </Link>
                        <Link href="#pricing" className="flex items-center gap-2 transition-all duration-200 ease-in-out text-foreground font-medium hover:text-primary hover:bg-primary/30 px-4 py-2 rounded-md hover:scale-105 transform group">
                            <Image src="/pricing.svg" alt="Pricing Icon" width={18} height={18} className="opacity-80 group-hover:opacity-100 transition-opacity" />
                            定价
                        </Link>
                    </nav>

                    {/* Right Section: Actions */}
                    <div className="flex items-center space-x-3">
                        {/* Language Selector Icon */}
                        <button
                            aria-label="Select language"
                            className="p-2 rounded-md hover:bg-primary/25 hover:scale-110 transform transition-all duration-200 ease-in-out group cursor-pointer"
                        >
                            <Image src="/globe.svg" alt="Language selection" width={24} height={24} className="opacity-75 group-hover:opacity-100 transition-opacity" />
                        </button>

                        {status === "loading" ? (
                            // Improved Skeleton Loader:
                            // Mimics the size of the login button or avatar area
                            <div className="flex items-center space-x-3">
                                <div className="h-10 w-10 rounded-full bg-muted animate-pulse"></div> {/* Avatar-like skeleton */}
                                {/* Or, if unauthenticated is more common initial state:
                                <div className="h-10 px-6 py-3 bg-muted rounded-lg animate-pulse w-24"></div> Login button-like skeleton
                                Let's use a generic one that covers both possibilities or a small circle for avatar
                                */}
                            </div>
                        ) : session?.user ? (
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" className="relative h-10 w-10 rounded-full p-0 cursor-pointer group transition-all duration-200 ease-in-out hover:shadow-primary/40 hover:shadow-lg hover:scale-105">
                                        <Avatar className="h-10 w-10 border-2 border-primary/50 group-hover:border-primary transition-all duration-200 ease-in-out">
                                            <AvatarImage src={session.user.image || ""} alt={session.user.name || "User"} />
                                            <AvatarFallback className="bg-primary text-primary-foreground font-semibold">
                                                {getInitials(session.user.name)}
                                            </AvatarFallback>
                                        </Avatar>
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent className="w-48 bg-background/95 backdrop-blur-md border-primary/30 shadow-xl" align="end" forceMount> {/* Reduced width, added more styling */}
                                    <DropdownMenuLabel className="font-normal px-3 py-2 text-center"> {/* Centered label text, adjusted padding */}
                                        <div className="flex flex-col items-center space-y-1"> {/* Centered content */}
                                            <p className="text-sm font-semibold leading-none text-foreground"> {/* Increased font weight */}
                                                {session.user.name || "用户"}
                                            </p>
                                            {session.user.email && (
                                                <p className="text-xs leading-none text-muted-foreground">
                                                    {session.user.email}
                                                </p>
                                            )}
                                        </div>
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator className="bg-primary/20" />
                                    {/* Added custom class for item styling, ensuring flex and justify-center for text centering */}
                                    <DropdownMenuItem
                                        onClick={() => console.log("Navigate to user settings")}
                                        className="px-3 py-2.5 text-sm flex justify-center items-center text-foreground hover:bg-primary/20 hover:text-primary focus:bg-primary/25 focus:text-primary cursor-pointer transition-colors duration-150 ease-in-out"
                                    >
                                        <span>用户设置</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator className="bg-primary/20" />
                                    <DropdownMenuItem
                                        onClick={() => signOut({ callbackUrl: '/' })}
                                        className="px-3 py-2.5 text-sm flex justify-center items-center text-destructive hover:bg-destructive/20 hover:text-destructive focus:bg-destructive/25 focus:text-destructive cursor-pointer transition-colors duration-150 ease-in-out"
                                    >
                                        <span>登出</span>
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        ) : (
                            <Button
                                onClick={() => setIsLoginModalOpen(true)}
                                className="bg-primary text-primary-foreground font-medium px-6 py-3 text-base rounded-lg shadow-[0_5px_0_0_rgba(0,0,0,0.3),0_0_25px_8px_oklch(var(--primary)/0.5)] hover:bg-accent hover:text-accent-foreground hover:shadow-[0_3px_0_0_rgba(0,0,0,0.3),0_0_25px_10px_oklch(var(--accent)/0.6)] hover:translate-y-[2px] transition-all duration-150 ease-in-out transform button-shake-hover group cursor-pointer"
                            >
                                登录
                            </Button>
                        )}
                        {/* Mobile Menu (optional, for later) */}
                    </div>
                </div>
            </header>
            <LoginModal open={isLoginModalOpen} onOpenChange={setIsLoginModalOpen} />
        </>
    );
};
