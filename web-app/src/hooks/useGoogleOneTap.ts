"use client";

import { signIn, useSession } from 'next-auth/react';
import { useEffect, useRef } from 'react';

// The @types/google.accounts package should provide the 'google' global object type
// and types like google.accounts.id.CredentialResponse and google.accounts.id.PromptMomentNotification.

export function useGoogleOneTap() {
    const { data: session, status } = useSession();
    const oneTapInitialized = useRef(false);

    useEffect(() => {
        if (status === 'unauthenticated' && !oneTapInitialized.current) {
            // Check if the google global object is available from the GIS script
            if (typeof window.google !== 'undefined' && window.google.accounts && window.google.accounts.id) {
                try {
                    window.google.accounts.id.initialize({
                        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID as string,
                        callback: async (response: google.accounts.id.CredentialResponse) => { // Use type from @types/google.accounts
                            console.log('Google One Tap CredentialResponse:', response);
                            if (response.credential) {
                                // Attempt to sign in with NextAuth.js using the ID token
                                // We might need a custom CredentialsProvider for this,
                                // or see if the Google provider can handle an id_token directly.
                                // For now, let's try with the 'google' provider and see if it can pick up the context
                                // or if we need to pass the credential.
                                // The standard way is to send this credential to your backend
                                // and verify it there, then sign the user in.
                                // A common pattern with NextAuth.js is to use a Credentials provider.

                                // Let's try a more direct approach first, then refine if needed.
                                // This might not work directly as 'google' provider expects an OAuth flow.
                                // We will likely need to create a custom API endpoint or use CredentialsProvider.

                                // For now, just log it and we'll implement the signIn call in the next step
                                // after deciding on CredentialsProvider vs custom endpoint.
                                console.log('ID Token received from One Tap:', response.credential);

                                // Placeholder for actual sign-in logic:
                                // Option 1: signIn with 'google' (might not work as expected with just token)
                                // await signIn('google', { id_token: response.credential, callbackUrl: '/' });

                                // Option 2: signIn with a 'credentials' provider (more robust)
                                // This would involve sending response.credential to our backend via the credentials provider
                                await signIn('credentials', {
                                    credential: response.credential,
                                    redirect: false, // Handle redirect manually or based on response
                                    callbackUrl: '/',
                                });
                                // After signIn, NextAuth should handle session creation and redirect if configured.
                                // If redirect: false, you might need to router.push('/') or handle errors.
                            } else {
                                console.error('Google One Tap: No credential returned in response.');
                            }
                        },
                        // auto_select: true, // Automatically select the only Google account if available
                        // cancel_on_tap_outside: false, // Default is true
                        // theme: 'outline', // or 'filled_blue', 'filled_black'
                        // prompt_parent_id: 'oneTapParentContainer' // Optional: if you want to render it in a specific container
                    });

                    // Prompt the user to select an account.
                    window.google.accounts.id.prompt((notification: google.accounts.id.PromptMomentNotification) => { // Use type from @types/google.accounts
                        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                            console.warn('Google One Tap prompt was not displayed or skipped:', notification.getNotDisplayedReason() || notification.getSkippedReason());
                            // Potentially fallback to showing the manual "Sign in with Google" button more prominently
                        } else {
                            console.log('Google One Tap prompt displayed.');
                        }
                    });

                    oneTapInitialized.current = true; // Mark as initialized to prevent re-initialization
                } catch (error) {
                    console.error('Error initializing Google One Tap:', error);
                }
            } else {
                // It's possible the script hasn't loaded when this effect runs.
                // Consider adding a small delay or a listener for script load if this warning appears frequently.
                console.warn('Google Identity Services script not loaded yet or `window.google.accounts.id` not available.');
            }
        }

        // Cleanup function if needed, though One Tap usually manages its own lifecycle
        // No specific cleanup action identified for now for One Tap initialization/prompt
        return () => {
            // if (window.google && window.google.accounts && window.google.accounts.id && oneTapInitialized.current) {
            //   // Potentially hide or cancel the prompt if the component unmounts
            //   // window.google.accounts.id.cancel(); // Example, check GIS docs for proper cleanup
            // }
        };
    }, [status]); // Re-run effect if authentication status changes
}
