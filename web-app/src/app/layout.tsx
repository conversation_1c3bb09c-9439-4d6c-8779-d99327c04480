import AuthProvider from "@/components/auth/AuthProvider"; // Import the new AuthProvider
import type { Metadata } from "next";
import Script from "next/script"; // Import Script component
// import { <PERSON><PERSON><PERSON> } from "next/font/google"; // Temporarily comment out
import "./globals.css";

// const geistSans = Geist({ // Temporarily comment out
//   variable: "--font-geist-sans",
//   subsets: ["latin", "latin-ext"], // Added latin-ext for broader character support
// });

export const metadata: Metadata = {
  title: "LuckyX AI - AI 图像生成与编辑平台",
  description: "通过自然语言描述轻松创造和修改图像。",
  icons: {
    icon: [
      { url: "/favicon.svg", type: "image/svg+xml" },
      { url: "/icon-192x192.png", type: "image/png", sizes: "192x192" }, // Example, assuming you have this
      { url: "/icon-32x32.png", type: "image/png", sizes: "32x32" }, // Example
      { url: "/icon-16x16.png", type: "image/png", sizes: "16x16" }, // Example
    ],
    apple: [
      { url: "/apple-icon.png", type: "image/png" },
    ],
    // other: [ // For manifest if you add one later
    //   { rel: 'manifest', url: '/site.webmanifest' },
    // ]
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" className="dark">
      {/* <body className={`${geistSans.variable} font-sans antialiased`}> */}
      <body className={`font-sans antialiased`}> {/* Use fallback font style */}
        <AuthProvider>
          {children}
        </AuthProvider>
        <Script src="https://accounts.google.com/gsi/client" strategy="beforeInteractive" />
      </body>
    </html>
  );
}
