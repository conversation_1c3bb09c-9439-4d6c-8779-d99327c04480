import { Navbar } from "@/components/layout/Navbar";
import { FaqSection } from "@/components/sections/FaqSection";
import { FeaturesSection } from "@/components/sections/FeaturesSection";
import { GenerationForm } from "@/components/sections/GenerationForm";
import { HistorySection } from "@/components/sections/HistorySection";
import { InspirationGallerySection } from "@/components/sections/InspirationGallerySection";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen bg-background text-foreground">
      <Navbar />
      <main className="flex flex-1 flex-col items-center py-6 px-3 sm:py-12 sm:px-6 md:py-24 md:px-8"> {/* Adjusted horizontal padding */}
        <div className="mt-2 mb-12 text-center"> {/* Adjusted mt-4 to mt-2 for less spacing from Navbar */}
          <h1 className="text-5xl font-extrabold tracking-normal text-primary sm:text-6xl md:text-7xl flex items-center justify-center">
            <img src="/favicon.svg" alt="LuckyX AI Logo" className="h-12 w-12 mr-4 sm:h-14 sm:w-14 md:h-16 md:w-16" />
            LuckyX AI
          </h1>
          <p className="mt-6 text-lg leading-relaxed text-muted-foreground sm:text-xl sm:max-w-2xl sm:mx-auto md:text-2xl lg:mx-0">
            ✨ 告别复杂提示词，一键释放 AI 顶尖图像创作力
            <br />
            ✨ 无缝融合生成与编辑，让您的创意立即成为现实
          </p>
          <div className="mt-8 flex flex-wrap justify-center gap-3">
            <span className="bg-primary/10 text-primary text-sm font-semibold px-4 py-2 rounded-full shadow-md hover:bg-primary/20 transition-all cursor-default animate-float-1">SOTA</span>
            <span className="bg-secondary/10 text-secondary text-sm font-semibold px-4 py-2 rounded-full shadow-md hover:bg-secondary/20 transition-all cursor-default animate-float-2">秒级生成</span>
            <span className="bg-accent/10 text-accent text-sm font-semibold px-4 py-2 rounded-full shadow-md hover:bg-accent/20 transition-all cursor-default animate-float-3">零门槛创作</span>
            <span className="bg-[var(--chart-4)]/10 text-[var(--chart-4)] text-sm font-semibold px-4 py-2 rounded-full shadow-md hover:bg-[var(--chart-4)]/20 transition-all cursor-default animate-float-4">一体化编辑</span>
            <span className="bg-[var(--chart-5)]/10 text-[var(--chart-5)] text-sm font-semibold px-4 py-2 rounded-full shadow-md hover:bg-[var(--chart-5)]/20 transition-all cursor-default animate-float-5">风格转换</span>
          </div>
        </div>

        <GenerationForm />

        <HistorySection />

        <FeaturesSection />

        <InspirationGallerySection />

        <FaqSection />

      </main>
      <footer className="py-6 md:px-8 md:py-0 bg-background border-t border-border">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
          <p className="text-balance text-center text-sm leading-loose text-muted-foreground md:text-left">
            © {new Date().getFullYear()} LuckyX AI. 保留所有权利.
          </p>
        </div>
      </footer>
    </div>
  );
}
