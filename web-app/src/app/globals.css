@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.6207 0.1926 258.04);
  /* Blue #2196F3 */
  --primary-foreground: oklch(0.985 0 0);
  /* Light text for blue */
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.749 0.191 59.93);
  /* Orange #FF9800 */
  --accent-foreground: oklch(0.141 0.005 285.823);
  /* Dark text for orange */
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.015 286.067);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.015 286.067);
}

.dark {
  --radius: 0.625rem;
  /* Retain radius from :root if not redefined for dark */

  /* Cyberpunk Theme */
  --background: oklch(0.091 0.051 293.7);
  /* #0D0221 */
  --foreground: oklch(0.946 0 0);
  /* #F0F0F0 */

  --card: oklch(0.141 0.061 293.7);
  /* #1A0A3A */
  --card-foreground: oklch(0.946 0 0);
  /* #F0F0F0 */

  --popover: oklch(0.141 0.061 293.7);
  /* #1A0A3A */
  --popover-foreground: oklch(0.946 0 0);
  /* #F0F0F0 */

  --primary: oklch(0.879 0.177 89.9);
  /* #FFD600 (Cyber Yellow) */
  --primary-foreground: oklch(0.091 0.051 293.7);
  /* #0D0221 (Dark BG for text on yellow) */

  --secondary: oklch(0.876 0.141 220);
  /* #00E0F0 (Cyber Cyan) */
  --secondary-foreground: oklch(0.091 0.051 293.7);
  /* #0D0221 (Dark BG for text on cyan) */

  --muted: oklch(0.191 0.061 293.7);
  /* #2A1A4A (Lighter than card for muted BG) */
  --muted-foreground: oklch(0.676 0.008 270);
  /* #A0A0B0 (Light grey muted text) */

  --accent: oklch(0.679 0.279 327.8);
  /* #F500FF (Cyber Magenta/Pink) */
  --accent-foreground: oklch(0.946 0 0);
  /* #F0F0F0 (Light text on magenta) */

  --destructive: oklch(0.604 0.231 19.7);
  /* #FF003C (Vibrant Red) */
  --destructive-foreground: oklch(0.946 0 0);
  /* #F0F0F0 */

  --border: oklch(0.191 0.061 293.7);
  /* #2A1A4A */
  --input: oklch(0.141 0.061 293.7);
  /* #1A0A3A (Same as card BG) */
  --ring: oklch(0.879 0.177 89.9 / 0.7);
  /* #FFD600 (Primary with alpha for focus) */

  /* Cyberpunk Chart colors */
  --chart-1: oklch(0.879 0.177 89.9);
  /* Cyber Yellow */
  --chart-2: oklch(0.876 0.141 220);
  /* Cyber Cyan */
  --chart-3: oklch(0.679 0.279 327.8);
  /* Cyber Magenta */
  --chart-4: oklch(0.908 0.171 160);
  /* Spring Green #00FF7F */
  --chart-5: oklch(0.691 0.158 59.9);
  /* Dark Orange #FF8C00 */

  /* Cyberpunk Sidebar colors */
  --sidebar: oklch(0.06 0.05 293.7);
  /* #0A011A (Even darker purple) */
  --sidebar-foreground: oklch(0.946 0 0);
  /* #F0F0F0 */
  --sidebar-primary: oklch(0.879 0.177 89.9);
  /* Cyber Yellow */
  --sidebar-primary-foreground: oklch(0.091 0.051 293.7);
  /* Dark BG */
  --sidebar-accent: oklch(0.679 0.279 327.8);
  /* Cyber Magenta */
  --sidebar-accent-foreground: oklch(0.946 0 0);
  /* Light Text */
  --sidebar-border: oklch(0.141 0.061 293.7);
  /* Card BG as border */
  --sidebar-ring: oklch(0.879 0.177 89.9 / 0.7);
  /* Primary with alpha */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-1px);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(1px);
  }
}

/* Keep the keyframes as they are, they seem fine */
/* @keyframes shake { ... } */

/* New class to apply shake animation on hover */
.button-shake-hover:hover {
  animation: shake 0.5s cubic-bezier(.36, .07, .19, .97) both;
}

@keyframes float-1 {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-6px);
  }
}

@keyframes float-2 {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-4px);
  }
}

@keyframes float-3 {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-8px);
  }
}

.animate-float-1 {
  animation: float-1 3s ease-in-out infinite;
}

.animate-float-2 {
  animation: float-2 3.5s ease-in-out infinite;
}

.animate-float-3 {
  animation: float-3 2.5s ease-in-out infinite;
}

.animate-float-4 {
  animation: float-2 4s ease-in-out infinite reverse;
  /* Use float-2 but reverse and different duration */
}

.animate-float-5 {
  animation: float-1 3.2s ease-in-out infinite reverse;
  /* Use float-1 but reverse and different duration */
}
