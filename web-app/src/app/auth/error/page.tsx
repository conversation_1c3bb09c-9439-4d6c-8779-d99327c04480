"use client";

import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useSearchParams } from "next/navigation";

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");

  let errorMessage = "登录过程中发生错误";
  
  // 根据错误类型显示不同的错误信息
  if (error === "OAuthSignin") errorMessage = "OAuth 签名过程中发生错误";
  if (error === "OAuthCallback") errorMessage = "OAuth 回调过程中发生错误";
  if (error === "OAuthCreateAccount") errorMessage = "创建 OAuth 账户时发生错误";
  if (error === "EmailCreateAccount") errorMessage = "创建邮箱账户时发生错误";
  if (error === "Callback") errorMessage = "回调过程中发生错误";
  if (error === "OAuthAccountNotLinked") errorMessage = "此邮箱已经使用其他方式登录";
  if (error === "EmailSignin") errorMessage = "邮箱登录过程中发生错误";
  if (error === "CredentialsSignin") errorMessage = "登录凭证无效";
  if (error === "SessionRequired") errorMessage = "此操作需要登录";
  if (error === "Default") errorMessage = "登录过程中发生未知错误";

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white dark:bg-card p-4">
      <div className="max-w-md w-full text-center p-6 bg-background rounded-lg shadow-lg border border-border">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-12 w-12 text-destructive mx-auto mb-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
          />
        </svg>
        <h1 className="text-2xl font-bold text-foreground mb-2">登录失败</h1>
        <p className="text-muted-foreground mb-6">{errorMessage}</p>
        <div className="flex flex-col gap-2">
          <Button asChild className="w-full">
            <Link href="/">返回首页</Link>
          </Button>
          <Button variant="outline" asChild className="w-full">
            <Link href="/">重新尝试登录</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
