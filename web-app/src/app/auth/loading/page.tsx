"use client";

import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

export default function AuthLoadingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 获取URL参数
  const callbackUrl = searchParams.get("callbackUrl") || "/";
  const provider = searchParams.get("provider") || "google";

  // 自动触发登录
  useEffect(() => {
    const initiateSignIn = async () => {
      try {
        // 自动触发登录
        await signIn(provider, { callbackUrl, redirect: true });
      } catch (error) {
        console.error("登录失败:", error);
        // 如果登录失败，返回首页
        router.push("/");
      }
    };

    initiateSignIn();

    // 如果用户停留在此页面超过10秒，自动返回首页
    const timeout = setTimeout(() => {
      router.push("/");
    }, 10000);

    return () => clearTimeout(timeout);
  }, [router, provider, callbackUrl]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white dark:bg-card">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-t-transparent border-primary rounded-full animate-spin mx-auto mb-8"></div>
        <h1 className="text-2xl font-bold text-foreground mb-2">正在登录中...</h1>
        <p className="text-muted-foreground">请稍候，正在连接到 Google 认证服务</p>
      </div>
    </div>
  );
}
