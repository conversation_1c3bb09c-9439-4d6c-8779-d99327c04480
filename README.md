# ImageCreator: AI 图像生成与编辑平台

## 项目简介

ImageCreator 是一个旨在融合 AI 图像生成与自然语言图像编辑功能的一体化平台。它允许用户通过简单的文字描述即可轻松创造全新的图像或修改现有图像。项目以用户体验为核心，提供直观、便捷的界面，致力于降低 AI 图像创作的技术门槛，使专业人士和普通用户都能轻松利用先进的 AI 技术实现创意构想。

## 技术栈

本项目主要包含以下技术：

*   **前端应用 (`./web-app/`)**:
    *   **Next.js 15**: React 框架，支持 App Router, 服务端渲染 (SSR) 和静态站点生成 (SSG)。
    *   **React 19**: 用于构建用户界面的 JavaScript 库。
    *   **TypeScript**: 为 JavaScript 添加静态类型。
    *   **Tailwind CSS v4**: 一个实用优先的 CSS 框架。
    *   **Shadcn UI**: 基于 Radix UI 和 Tailwind CSS 构建的可重用 UI 组件库。
    *   **Zustand / React Context API**: 用于状态管理。
*   **AI 服务集成**:
    *   **Replicate API**: 用于接入各种 AI 图像生成和编辑模型。
*   **开发工具**:
    *   **ESLint**: 代码风格和错误检查。
    *   **Prettier**: 代码格式化。

详细的技术栈和架构信息请参考 `memory-bank/techContext.md` 和 `memory-bank/systemPatterns.md`。

## 如何运行

前端 Web 应用位于 `web-app` 子目录中。

1.  **进入 Web 应用目录**:
    ```bash
    cd web-app
    ```

2.  **安装依赖**:
    ```bash
    npm install
    # 或者 yarn install / pnpm install
    ```

3.  **启动开发服务器**:
    ```bash
    npm run dev
    # 或者 yarn dev / pnpm dev
    ```
    应用默认会在 `http://localhost:3000` 启动。

## 项目文档 (Memory Bank)

本项目使用 "Memory Bank" 系统来维护其核心文档和上下文。所有相关的项目信息，包括目标、架构、技术决策和当前进展，都记录在 `memory-bank/` 目录下的 Markdown 文件中。

建议在开始任何开发工作或想要深入了解项目时，首先查阅 `memory-bank/` 中的文档，特别是 `memory-bank/projectbrief.md` 和 `memory-bank/activeContext.md`。
