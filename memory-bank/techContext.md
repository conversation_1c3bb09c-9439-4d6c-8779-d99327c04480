# 技术背景

## 使用技术栈

### 前端技术

1. **核心框架**：
   - **React 19**：利用最新的并发渲染特性和自动批处理功能
   - **Next.js 15**：采用App Router架构，实现服务端渲染和静态生成，提供SEO友好的架构

2. **状态管理**：
   - **React Context API**：轻量级状态管理
   - **Zustand**：简单高效的状态管理库，适用于复杂状态逻辑

3. **UI组件库**：
   - **Tailwind CSS v4**：实用优先的CSS框架，提高开发效率
   - **Shadcn UI**：基于Radix UI构建的可定制组件库，提供高质量的设计系统和组件
   - **Headless UI**：无样式组件库，提供高度可定制的基础组件
   - **Framer Motion**：实现流畅的动画和过渡效果

4. **图像处理**：
   - **react-dropzone**：处理图像上传
   - **next/image**：优化图像加载和展示，提供SEO友好的图像处理
   - **fabric.js**：基本的前端图像编辑功能

5. **开发工具**：
   - **TypeScript**：静态类型检查，提升代码质量
   - **ESLint** 和 **Prettier**：代码质量和风格保证
   - **Jest** 和 **React Testing Library**：前端单元测试和集成测试
   - **SEO工具**：Lighthouse SEO分析、Next.js内置元数据API和结构化数据支持

### 后端技术

1. **API服务**：
   - **Next.js API Routes**：统一部署的API层，处理大部分API请求
   - **Node.js**：高性能JavaScript运行时
   - **Edge Functions**（可选）：处理低延迟需求的功能点

2. **AI服务集成**：
   - **Replicate API**：用于接入各种AI图像生成和编辑模型
   - **WebSockets**：实时进度更新和通知
   - **API适配层**：处理与Replicate API的交互和响应解析

3. **数据存储**：
   - **Supabase**：PostgreSQL数据库。将用于存储应用特定的用户数据，如用户偏好、订阅状态等（如果未来实现）。NextAuth.js 的核心认证数据（如用户身份、账户关联）将不通过 Supabase Adapter 或其相关表进行管理。
   - **会话管理**：采用纯 **JWT (JSON Web Token) 策略** (`session: { strategy: "jwt" }` in NextAuth.js)，不使用数据库适配器。所有会话信息包含在加密的JWT Cookie中。
   - **临时存储**：处理图像上传和生成过程，不进行永久存储

4. **AI模型使用**：
   - 通过**Replicate平台**使用的模型：
     - **Stable Diffusion**系列模型：用于图像生成
     - **InstructPix2Pix**等模型：用于图像编辑
     - **ControlNet**系列：用于精确控制生成结果

### 开发和部署环境

1. **开发环境**：
   - **Docker**：容器化开发环境
   - **GitLab/GitHub**：版本控制和CI/CD
   - **VSCode**：主要IDE，配置团队一致的开发设置

2. **部署架构**：
   - **Next.js统一部署**：在Vercel或类似平台部署Next.js应用
   - **Serverless Functions**：按需扩展的API功能
   - **云服务提供商**：AWS/Azure/Google Cloud的服务集成

3. **监控和日志**：
   - **Prometheus**：系统和性能监控
   - **ELK Stack**：日志收集和分析
   - **Sentry**：错误跟踪和异常监控

## 技术约束

### 性能约束

1. **响应时间目标**：
   - 页面加载时间：< 2秒
   - 简单图像生成时间：< 10秒
   - 复杂图像编辑时间：< 30秒

2. **并发处理能力**：
   - 支持同时处理至少500个用户请求
   - 队列管理机制处理高峰期流量

3. **资源消耗限制**：
   - 根据业务增长阶段制定不同的GPU使用预算
   - 实施模型精简和量化，减少计算资源需求

### 兼容性要求

1. **浏览器支持**：
   - 现代浏览器（Chrome, Firefox, Safari, Edge最新两个版本）
   - 不支持IE和旧版浏览器

2. **设备适配**：
   - 桌面设备（最低1024px宽度）
   - 平板设备（最低768px宽度）
   - 移动设备（最低320px宽度）

3. **网络条件**：
   - 优化低带宽环境下的体验
   - 实现渐进式加载和部分离线功能

### 安全要求

1. **数据保护**：
   - 传输加密（HTTPS/TLS）
   - 存储加密（敏感信息）
   - 合规性考量（GDPR, CCPA等）

2. **权限控制**：
   - 基于角色的访问控制
   - 资源使用限额和防滥用机制

3. **内容安全**：
   - 违规内容过滤
   - 用户上传内容审核
   - 不存储用户上传的图像或生成的图像

### 扩展性限制

1. **API集成约束**：
   - Replicate API调用配额和频率限制
   - 模型版本管理和兼容性考量
   - 处理API延迟和可用性问题的策略

2. **API成本管理**：
   - API调用成本监控和控制机制
   - 基于用户层级的API使用策略
   - 缓存常见请求结果减少API调用

## 依赖关系

### 外部依赖

1. **AI服务依赖**：
   - **Replicate API**：主要的AI能力提供者
   - Replicate支持的各类模型：图像生成、编辑、风格转换等
   - API密钥管理和安全存储

2. **云服务依赖**：
   - 对象存储：图像文件存储
   - CDN：内容分发
   - 无需自管理GPU计算资源

3. **第三方服务**：
   - **身份认证**：**NextAuth.js** (深度集成Next.js, 支持多种登录方式如Google, 开源免费)。已配置为使用纯 **JWT 会话策略**，不使用数据库适配器。
   - **数据存储**：**Supabase** (PostgreSQL) 用于存储应用特定的用户数据（如用户偏好、未来的订阅信息等），但不用于 NextAuth.js 的核心认证表。
   - **安全验证**：Cloudflare Turnstile（防止AI生成功能被滥用）
   - **支付处理**：Creem (https://www.creem.io/) (将与Supabase集成处理订阅状态)
   - **分析工具**：Google Analytics, Mixpanel

### 内部依赖

1. **服务间依赖**：
   - Next.js应用（整合前端和API Routes）
   - API Routes → Replicate API服务
   - API Routes → 数据存储服务

2. **数据流依赖**：
   - 用户输入 → 模型处理 → 结果存储 → 用户展示
   - 编辑请求 → 原图加载 → 处理 → 结果存储

## 工具使用模式

### 开发工具链

1. **代码管理**：
   - Git工作流：feature branches + pull requests
   - 代码审查流程和标准

2. **构建流程**：
   - 前端：Next.js内置构建工具
   - 容器化：Docker多阶段构建
   - CI/CD：自动化测试和部署

3. **质量保证**：
   - 自动化测试策略：单元测试、集成测试、E2E测试
   - 代码质量工具：ESLint, SonarQube
   - 性能监测：Lighthouse, WebPageTest

### API服务管理

1. **模型选择策略**：
   - 评估Replicate上可用模型的性能和适用性
   - 针对不同任务选择最佳模型
   - 跟踪模型更新和新模型发布

2. **API调用优化**：
   - 请求批处理与合并
   - 智能超时和重试机制
   - 结果缓存和复用

3. **成本效益优化**：
   - API调用成本分析和优化
   - 根据用户层级分配API资源
   - 实时监控API使用量和成本

### 运维工具

1. **部署策略**：
   - 蓝绿部署
   - 金丝雀发布
   - 回滚机制

2. **监控体系**：
   - 系统健康监控
   - 用户体验监控
   - 成本监控

3. **灾备策略**：
   - 数据备份方案
   - 服务冗余设计
   - 故障恢复流程
