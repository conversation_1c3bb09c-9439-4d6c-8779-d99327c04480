# 系统架构与设计模式

## 整体架构

图像生成与编辑平台采用基于Next.js的统一部署架构，结合轻量级API和外部服务集成，确保系统的可扩展性、可维护性和性能表现。系统由以下主要部分组成：

```
                    ┌─────────────────────┐
                    │   Next.js 应用      │
                    │                     │
┌───────────────┐   │  ┌───────────────┐  │
│   客户端      │   │  │  前端页面/组件 │  │
│  (浏览器)     │◄──┼─►│               │  │
└───────────────┘   │  └───────┬───────┘  │
                    │          │          │
                    │          ▼          │
                    │  ┌───────────────┐  │
                    │  │   API Routes  │  │
                    │  └───────┬───────┘  │
                    └──────────┼──────────┘
                               │
                               ▼
         ┌────────────────────────────────────┐
         │      外部服务集成层                │
         └───────────────┬────────────────────┘
                         │
         ┌───────────────▼────────────────────┐
         │   Replicate API 代理服务           │
         └───────────────┬────────────────────┘
                         │
                         ▼
         ┌────────────────────────────────────┐
         │     数据和状态存储服务             │
         └────────────────────────────────────┘
```

### 主要组件

1. **Next.js应用（统一部署）**：
   - **前端页面/组件**：
     - 用户界面和交互层
     - 负责图像预览和基本处理
     - 实现响应式设计，支持多设备访问
   - **API Routes**：
     - 处理前端API请求
     - 实现用户认证和授权
     - 会话管理和状态控制
     - 服务调度和路由

2. **外部服务集成层**：
   - 请求路由和服务协调
   - 外部API调用封装
   - 错误处理和重试机制
   - 结果转换和格式化

3. **Replicate API代理服务**：
   - 与Replicate API集成
   - 处理模型选择和请求转发
   - 管理API密钥和调用限制
   - 实现结果缓存和请求优化

4. **数据和状态存储服务**：
   - 用户配置和偏好存储
   - 会话状态管理
   - 使用量统计和计费数据

## 关键技术决策

### 1. 前端框架选择

采用**React**作为前端框架，配合**Next.js**实现服务端渲染，提升初次加载性能和SEO表现。

**决策理由**：
- React生态系统成熟，组件化开发效率高
- Next.js提供了良好的服务端渲染支持
- 现代化的前端工具链，便于开发和维护
- 丰富的UI组件库可供选择

### 2. 部署架构决策

采用**Next.js统一部署**模式，集成前端和轻量级API服务。

**决策理由**：
- 简化初期开发和部署复杂度
- 利用Next.js的全栈能力处理前端渲染和简单API
- 避免多服务间的跨域和通信问题
- 降低运维负担，适合独立开发者资源限制

### 3. AI模型接入策略

采用**Replicate API接入**模式，利用云端托管的AI模型服务。

**决策理由**：
- 无需自建和维护AI模型基础设施
- 可直接使用最先进的开源模型
- 按需付费模式降低初期投入
- 无需处理复杂的模型部署和资源扩展

### 4. 数据存储方案

用户数据、配置及付费订阅信息将采用 **Supabase** (PostgreSQL) 进行存储。图像文件仍考虑使用**对象存储**服务（如果需要永久存储，但当前策略是不存储用户图像）。

**决策理由**：
- Supabase 提供慷慨的免费层，适合项目初期。
- PostgreSQL 是成熟的关系型数据库，适合结构化数据如用户信息和订阅。
- Supabase 提供简单的 API 和客户端库，并支持行级安全策略。
- 可视化管理界面方便数据管理。
- 对象存储服务专为大规模图像文件设计，成本效益高（如果未来决定存储图像）。
- Supabase 将用于存储用户偏好、订阅状态等应用数据，但 NextAuth.js 的核心认证（用户身份、账户关联）将不依赖 Supabase Adapter 或其创建的表。
- **会话管理采用纯 JWT (JSON Web Token) 策略**。NextAuth.js 不会使用数据库（如Supabase中的`users`, `accounts`, `sessions`, `verification_tokens`表）来存储其核心认证数据或会话状态。所有必要的会话信息都包含在加密的JWT Cookie中。


## 设计模式

### 1. 命令模式 (Command Pattern)

在图像编辑流程中实现命令模式，将每个编辑操作封装为独立的命令对象。

**应用场景**：
- 用户通过自然语言指令触发的图像编辑操作
- 支持操作的撤销和重做功能
- 编辑历史记录和回溯

### 2. 策略模式 (Strategy Pattern)

在图像生成服务中使用策略模式，根据不同场景选择最适合的生成模型和参数。

**应用场景**：
- 根据用户选择的风格应用不同的生成策略
- 根据服务层级选择不同质量和速度的处理方式
- 自适应模型选择以优化资源使用

### 3. 观察者模式 (Observer Pattern)

实现系统状态监控和任务进度通知。

**应用场景**：
- 长时间运行的图像生成任务进度更新
- 系统状态变化通知
- 用户操作的实时反馈

### 4. 代理模式 (Proxy Pattern)

用于实现图像缓存和延迟加载机制。

**应用场景**：
- 图像预览的延迟加载
- 已生成图像的缓存代理
- API调用的智能缓存

### 5. 工厂模式 (Factory Pattern)

用于创建不同类型的图像处理组件和过滤器。

**应用场景**：
- 动态创建适合特定任务的处理器
- 根据用户需求构建自定义的处理流程
- 扩展系统支持的图像操作类型

## 关键实现路径

### 1. 图像生成流程

```
用户输入 → 文本分析 → 提示优化 → 模型选择 → 图像生成 → 后处理 → 结果返回
```

### 2. 图像编辑流程

```
图像上传 → 编辑指令解析 → 操作映射 → 模型处理 → 结果预览 → 用户确认 → 最终处理
```

### 3. 混合创作流程

```
图像上传 → 文本描述 → 融合参数计算 → 条件生成 → 结果优化 → 用户反馈 → 迭代优化
```

### 4. 用户认证流程 (使用 NextAuth.js - JWT 策略)

**前端交互:**
1. 用户点击导航栏「登录」按钮。
2. 应用弹出登录模态框，框内显示「使用 Google 登录」按钮。
3. 用户点击模态框中的「使用 Google 登录」按钮。
4. 前端调用 `signIn('google')`。

**后端及认证流程:**
```mermaid
sequenceDiagram
    participant User
    participant Browser (Next.js Frontend)
    participant NextAuth.js API Route
    participant Google

    Browser->>NextAuth.js API Route: Initiates Google Sign-In (via signIn('google'))
    NextAuth.js API Route->>Google: Redirects user or opens popup for Google Auth
    User->>Google: Authenticates with Google credentials
    Google-->>NextAuth.js API Route: Returns auth code/token
    NextAuth.js API Route->>Google: Verifies token, fetches user profile
    Google-->>NextAuth.js API Route: User profile (name, email, avatar)
    NextAuth.js API Route->>Browser: Generates JWT (containing user profile info), sets session cookie (containing JWT)
    Browser->>User: Updates UI (closes modal, shows avatar, enables dropdown)

    User->>Browser: (Navigates/Requests protected resource) Sends Cookie with JWT
    Browser->>NextAuth.js API Route: Request with JWT
    NextAuth.js API Route->>NextAuth.js API Route: Verifies JWT signature & expiry (NO DB call for session table or user/account tables for auth)
    NextAuth.js API Route-->>Browser: Returns user session data (from JWT) / Allows access

    User->>Browser: Clicks Avatar, then "Logout"
    Browser->>NextAuth.js API Route: Initiates Sign-Out
    NextAuth.js API Route->>Browser: Clears session cookie (containing JWT)
    Browser->>User: Updates UI (shows "Login" button)
```
**核心逻辑:**
- NextAuth.js 将配置为使用 Google OAuth 提供商。
- **会话策略设置为纯 JWT (`session: { strategy: "jwt" }`)，不使用数据库适配器。**
- 用户信息（ID, name, email, image）在首次登录时从Google获取，并直接编码到JWT中。
- JWT 包含所有必要的身份信息，并在客户端 Cookie 中安全存储。服务器通过验证 JWT 签名和有效期来确认用户身份，无需查询数据库。
- 用户登出时，客户端的 JWT Cookie 将被清除。

### 5. 扩展处理流程

```
负载监测 → 资源预测 → 动态扩容 → 任务重分配 → 性能优化 → 资源回收
```

## 组件关系图

```
┌──────────────┐       ┌───────────────┐      ┌────────────────┐
│  用户界面组件 │────›  │ API接口层     │────› │  服务编排层    │
└──────┬───────┘       └───────┬───────┘      └────────┬───────┘
       │                       │                       │
       │                       │                       │
       ▼                       ▼                       ▼
┌──────────────┐       ┌───────────────┐      ┌────────────────┐
│  图像预览    │       │  业务逻辑层   │      │   模型调度层   │
│  组件        │◄────› │              │◄────› │                │
└──────────────┘       └───────────────┘      └────────┬───────┘
                                                       │
                                                       │
                                              ┌────────▼───────┐
                                              │                │
                                              │   AI模型集群   │
                                              │                │
                                              └────────────────┘
```

## 扩展性设计

1. **插件化架构**：
   - 支持第三方模型集成
   - 自定义风格和处理器扩展
   - API扩展接口

2. **多租户支持**：
   - 隔离的资源分配
   - 可自定义的配置与品牌化
   - 独立的使用统计和计费

3. **服务弹性**：
   - 基于需求的自动伸缩
   - 跨区域部署支持
   - 灾难恢复设计

## 性能优化策略

1. **图像处理优化**：
   - 压缩算法选择
   - 渐进式加载策略
   - 图像格式自适应

2. **计算资源管理**：
   - GPU资源池化
   - 批处理优化
   - 预测式资源分配

3. **缓存策略**：
   - 多级缓存设计
   - 相似请求结果复用
   - 热点内容优先缓存
